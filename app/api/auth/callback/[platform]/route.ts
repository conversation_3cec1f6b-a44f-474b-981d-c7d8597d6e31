import {
  NextRequest, NextResponse,
} from 'next/server';
import { SupabaseTables } from '@/common/constants';
import { createClient } from '@/common/utils/supabase/server';
import { actions } from '@/common/utils/mixpanel';

export async function GET (
  request: NextRequest,
  { params }: { params: { platform: string } },
) {
  const { platform } = params;
  const searchParams = request.nextUrl.searchParams;
  const projectId = searchParams.get('projectId');
  const error = searchParams.get('error');
  const success = searchParams.get('success');
  const agentId = searchParams.get('agentId');
  const username = searchParams.get('username');
  const organizationId = searchParams.get('organizationId');
  const organizationName = searchParams.get('organizationName');
  const supabase = createClient();
  
  try {
    if (error) {
      return NextResponse.redirect(
        new URL(`/dashboard/accounts?error=${error}`, request.url),
      );
    }

    if (success === 'true' && projectId && agentId && username) {
      const { data: projectData } = await supabase
        .from(SupabaseTables.Projects)
        .select('*')
        .eq('project_id', projectId)


      if (projectData?.length) {
        const accounts = JSON.parse(projectData[0].accounts);

        accounts.accounts = accounts.accounts.map((account: any) => {
          if (account.platform.toLowerCase() === platform.toLowerCase()) {
            return {
              ...account,
              agentId,
              username,
              connected: true,
              ...(platform.toLowerCase() === 'linkedin' && organizationId && organizationName && {
                organizationId,
                organizationName,
                hasOrganizationAccess: false,
              }),
            };
          }
          return account;
        });
        await supabase
          .from(SupabaseTables.Projects)
          .update({
            accounts: JSON.stringify(accounts),
          })
          .eq('project_id', projectId);

        actions.track('Platform Connected', {
          platform: platform,
          has_organization_access: platform.toLowerCase() === 'linkedin' && organizationId ? false : undefined,
          timestamp: Date.now(),
        });
      }

      return NextResponse.redirect(
        new URL(`/dashboard/accounts?success=account_connected`, request.url),
      );
    }
  } catch (error) {
    console.error('Error updating project:', error);
    return NextResponse.redirect(
      new URL('/dashboard/accounts?error=update_failed', request.url),
    );
  }

  return NextResponse.redirect(
    new URL('/dashboard/accounts', request.url),
  );
}
