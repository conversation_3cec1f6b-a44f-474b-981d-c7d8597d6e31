"use client";

import { 
  useCallback, useRef, useState, useEffect,
} from "react";
import { DashboardLayout } from "@/common/components/organisms";
import { useProjects } from "@/common/hooks";
import {
  <PERSON><PERSON>ex<PERSON>,
  <PERSON><PERSON>, Loader,
} from '@/common/components/atoms';
import {
  FacebookIcon,
  InstagramIcon,
  LinkedInColorIcon,
  XIcon,
  YoutubeIcon,
} from "@/common/components/icons";
import { createClient } from "@/common/utils/supabase/client";
import { Tabs } from "@/common/components/molecules/tabs";
import toast from "react-hot-toast";
import { useMixpanelEvent } from '@/common/utils/mixpanel/eventTriggers';
import {
  startInstagramAgentUrl,
  startLinkedInAgentUrl,
  startTwitterAgentUrl,
  startTwitterBotAgentUrl,
  stopAgentUrl,
  toggleLinkedInAccountUrl,
} from "@/common/utils/network/endpoints";
import { SupabaseTables } from "@/common/constants";
import { SocialAccount } from "../types";
import { useSupabaseAuth } from "@/common/hooks/useSupabaseAuth";

export default function AccountsPage () {
  const {
    activeProject, fetchProjects, setActiveProject,
  } = useProjects();

  const { user } = useSupabaseAuth();
  const supabase = createClient();
  const [isLoading, setIsLoading] = useState(false);
  const { trackPlatformEvent } = useMixpanelEvent();

  const prevProjectIdRef = useRef<string | null>(null);

  useEffect(() => {
    if (activeProject) {
      prevProjectIdRef.current = activeProject.project_id;
    }
  }, [activeProject]);

  const getIconByPlatform = (platform: string) => {
    switch (platform.toLowerCase()) {
    case "twitter":
      return <XIcon />;
    case "linkedin":
      return <LinkedInColorIcon />;
    case "facebook":
      return <FacebookIcon />;
    case "instagram":
      return <InstagramIcon />;
    case "youtube":
      return <YoutubeIcon />;
    default:
      return null;
    }
  };

  const handlePauseAgent = useCallback(
    async (agentId: string | null) => {
      if (agentId) {
        setIsLoading(true);
        try {
          const response = await fetch(
            stopAgentUrl.replace("%agentId%", agentId),
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
            },
          );
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const updatedAccounts = {
            accounts: activeProject?.accounts.map((account: SocialAccount) => {
              if (account.agentId === agentId) {
                return {
                  ...account,
                  agentId: null,
                  connected: false,
                };
              }
              return account;
            }),
          };

          await supabase
            .from(SupabaseTables.Projects)
            .update({
              accounts: JSON.stringify(updatedAccounts),
            })
            .eq("project_id", activeProject?.project_id);

          if (activeProject) {
            const updatedProject = {
              ...activeProject,
              accounts: updatedAccounts.accounts || [],
            };

            setActiveProject(updatedProject as any);
          }

          await fetchProjects(true);

          const disconnectedAccount = activeProject?.accounts.find(acc => acc.agentId === agentId);
          if (disconnectedAccount) {
            trackPlatformEvent('disconnected', {
              platform: disconnectedAccount.platform,
            });
          }

          toast.success(`Account disconnected successfully.`);
        } catch (error) {
          toast.error("Failed to disconnect account");
        } finally {
          setIsLoading(false);
        }
      }

    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [activeProject, fetchProjects, supabase],
  );

  const handleToggleAccountType = useCallback(
    async (account: SocialAccount) => {
      if (!account.organizationId || !account.organizationName) {
        toast.error("Organization information not available");
        return;
      }

      if (!account.agentId) {
        toast.error("Agent ID not available");
        return;
      }

      const newBusinessAccountState = !account.hasOrganizationAccess;
      setIsLoading(true);
      try {
        const response = await fetch(
          toggleLinkedInAccountUrl.replace("%agentId%", account.agentId),
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              businessAccount: newBusinessAccountState,
            }),
          },
        );

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Update the local state
        const updatedAccounts = {
          accounts: activeProject?.accounts.map((acc: SocialAccount) => {
            if (acc.agentId === account.agentId) {
              return {
                ...acc,
                hasOrganizationAccess: newBusinessAccountState,
              };
            }
            return acc;
          }),
        };

        await supabase
          .from(SupabaseTables.Projects)
          .update({
            accounts: JSON.stringify(updatedAccounts),
          })
          .eq("project_id", activeProject?.project_id);

        if (activeProject) {
          const updatedProject = {
            ...activeProject,
            accounts: updatedAccounts.accounts || [],
          };

          setActiveProject(updatedProject as any);
        }

        await fetchProjects(true);

        const accountType = newBusinessAccountState ? "business" : "personal";
        const accountName = newBusinessAccountState ? account.organizationName : account.username;
        toast.success(`Switched to ${accountType} account: ${accountName}`);
      } catch (error) {
        console.error("Error toggling account type:", error);
        toast.error("Failed to switch account type");
      } finally {
        setIsLoading(false);
      }
    },
    [activeProject, fetchProjects, supabase, setActiveProject],
  );

  const handleStartAgent = useCallback(
    async (platform: string) => {
      if (
        activeProject &&
        (activeProject.billing_status === "basic" ||
          activeProject.billing_status === "free")
      ) {
        if (platform === "LinkedIn") {
          setIsLoading(true);
          try {
            const response = await fetch(startLinkedInAgentUrl, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                projectName: activeProject.name,
                projectId: activeProject.project_id,
              }),
            });
            if (response) {
              const { url } = await response.json();
              if (url) {
                trackPlatformEvent('auth_started', {
                  platform: 'LinkedIn',
                });
                window.open(url, "_self");
              }
            }
          } catch (error) {
            toast.error("Failed to start LinkedIn agent");
          } finally {
            setIsLoading(false);
          }
        }
        if (platform === "Instagram") {
          setIsLoading(true);
          try {
            const response = await fetch(startInstagramAgentUrl, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                projectName: activeProject.name,
                projectId: activeProject.project_id,
              }),
            });
            if (response) {
              const { url } = await response.json();
              if (url) {
                trackPlatformEvent('auth_started', {
                  platform: 'Instagram',
                });
                window.open(url, "_self");
              }
            }
          } catch (error) {
            toast.error("Failed to start Instagram agent");
          } finally {
            setIsLoading(false);
          }
        }
        if (platform === "Twitter") {
          setIsLoading(true)
          try {
            const response = await fetch(startTwitterAgentUrl, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                projectName: activeProject.name,
                projectId: activeProject.project_id,
              }),
            });
            if (response) {
              const { url } = await response.json();
              if (url) {
                trackPlatformEvent('auth_started', {
                  platform: 'Twitter',
                });
                window.open(url, '_self');
              }
            }
          } catch (error) {
            toast.error('Failed to start LinkedIn agent');
          } finally {
            setIsLoading(false)
          }
        }
      } else {
        toast.error("You need to upgrade to the Basic plan to use this feature.");
        return;
      }
    },
    [activeProject],
  );

  const handleStartTwitterBot = useCallback(
    async () => {
      setIsLoading(true);
      try {
        const response = await fetch(startTwitterBotAgentUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            projectName: "twitter-bot-" + Math.random().toString(36).substring(2, 8),
            projectId: "bot-" + Date.now() + "-" + Math.random().toString(36).substring(2, 6),
          }),
        });
        if (response) {
          const { url } = await response.json();
          if (url) {
            window.open(url, "_self");
          }
        }
      } catch (error) {
        toast.error("Failed to start Twitter bot agent");
      } finally {
        setIsLoading(false);
      }
     
    },
    [],
  );

  const tabContent = {
    all: (
      <div className="mt-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {activeProject?.accounts?.length
            ? activeProject.accounts.filter(account => !['youtube', 'facebook'].includes(account.platform.toLowerCase())).map((account) => (
              <div
                key={account.platform}
                className="bg-white/5 flex flex-col justify-between border border-white/10 rounded-xl p-6 transition-all duration-200 hover:border-violets-are-blue/30"
              >
                <div className="flex items-center mb-4 w-full">
                  <div
                    className={`p-3 rounded-full ${
                      account.connected
                        ? "bg-violets-are-blue/20"
                        : "bg-white/5"
                    } mr-3`}
                  >
                    {getIconByPlatform(account.platform)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 justify-between">
                      <h3 className="text-white font-medium">
                        {account.platform}
                      </h3>
                      
                      {account.platform.toLowerCase() === "twitter" && account.connected &&
                       ((account as SocialAccount).hasPremium ||
                        (account as SocialAccount).isBlueVerified) && (
                        <div className="text-xs px-2 py-1 rounded-full bg-gradient-to-tr from-han-purple/80 to-tulip/80 text-white">Premium</div>
                      )}
                    </div>
                    <p
                      className={`text-sm ${
                        account.connected
                          ? "text-neutral-200 max-w-40 truncate"
                          : "text-gray-400"
                      }`}
                    >
                      {account.connected ?
                        (account.platform.toLowerCase() === "linkedin" && (account as SocialAccount).hasOrganizationAccess && (account as SocialAccount).organizationName ? (account as SocialAccount).organizationName : account.username)
                        : "Not Connected"}
                    </p>
                  </div>
                </div>
                <div className="space-y-2">
                  {account.connected ? (
                    <>
                      {account.platform.toLowerCase() === "linkedin" && (account as SocialAccount).organizationId && (account as SocialAccount).organizationName && (
                        <Button
                          variant="gradient"
                          size="sm"
                          width="w-full"
                          className="w-full"
                          onClick={() => handleToggleAccountType(account as SocialAccount)}
                        >
                          {(account as SocialAccount).hasOrganizationAccess
                            ? "Switch to Personal Account"
                            : "Switch to Business Account"}
                        </Button>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        width="w-full"
                        className="w-full"
                        onClick={() => handlePauseAgent(account.agentId)}
                      >
                        Disconnect
                      </Button>
                    </>
                  ) : (
                    <Button
                      variant="gradient"
                      size="sm"
                      width="w-full"
                      className="w-full"
                      onClick={() => handleStartAgent(account.platform)}
                    >
                      Connect
                    </Button>
                  )}
                </div>
              </div>
            ))
            : null}
        </div>
      </div>
    ),
    connected: (
      <div className="mt-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {activeProject?.accounts?.length
            ? activeProject.accounts
              .filter((account) => account.connected)
              .filter(account => !['youtube', 'facebook'].includes(account.platform.toLowerCase()))
              .map((account) => (
                <div
                  key={account.platform}
                  className="bg-white/5 flex flex-col justify-between border border-violets-are-blue/30 rounded-xl p-6 transition-all duration-200 hover:border-violets-are-blue/50"
                >
                  <div className="flex items-center mb-4">
                    <div className="p-3 rounded-full bg-violets-are-blue/20 mr-3">
                      {getIconByPlatform(account.platform)}
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="text-white font-medium">
                          {account.platform}
                        </h3>
                        {account.platform.toLowerCase() === "twitter" && account.connected &&
                        ((account as SocialAccount).hasPremium ||
                          (account as SocialAccount).isBlueVerified) && (
                          <span className="text-base"><AnimatedText text="• Premium" /></span>
                        )}
                      </div>
                      <p className="text-sm text-neutral-200 max-w-40 truncate">
                        {(account.platform.toLowerCase() === "linkedin" && (account as SocialAccount).hasOrganizationAccess && (account as SocialAccount).organizationName ? (account as SocialAccount).organizationName : account.username)}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    {account.connected ? (
                      <>
                        {account.platform.toLowerCase() === "linkedin" && (account as SocialAccount).organizationId && (account as SocialAccount).organizationName && (
                          <Button
                            variant="gradient"
                            size="sm"
                            width="w-full"
                            className="w-full"
                            onClick={() => handleToggleAccountType(account as SocialAccount)}
                          >
                            {(account as SocialAccount).hasOrganizationAccess
                              ? "Switch to Personal Account"
                              : "Switch to Business Account"}
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          width="w-full"
                          className="w-full"
                          onClick={() => handlePauseAgent(account.agentId)}
                        >
                          Disconnect
                        </Button>
                      </>
                    ) : (
                      <Button
                        variant="gradient"
                        size="sm"
                        width="w-full"
                        className="w-full"
                        onClick={() => handleStartAgent(account.platform)}
                      >
                        Connect
                      </Button>
                    )}
                  </div>
                </div>
              ))
            : null}
        </div>
      </div>
    ),
    disconnected: (
      <div className="mt-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {activeProject?.accounts?.length
            ? activeProject.accounts
              .filter((account) => !account.connected)
              .filter(account => !['youtube', 'facebook'].includes(account.platform.toLowerCase()))
              .map((account) => (
                <div
                  key={account.platform}
                  className="bg-white/5 border border-white/10 rounded-xl p-6 transition-all duration-200 hover:border-violets-are-blue/30"
                >
                  <div className="flex items-center mb-4">
                    <div className="p-3 rounded-full bg-white/5 mr-3">
                      {getIconByPlatform(account.platform)}
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="text-white font-medium">
                          {account.platform}
                        </h3>
                        {account.platform.toLowerCase() === "twitter" && account.connected &&
                        ((account as SocialAccount).hasPremium ||
                          (account as SocialAccount).isBlueVerified) && (
                          <span className="text-base"><AnimatedText text="• Premium" /></span>
                        )}
                      </div>
                      <p className="text-sm text-gray-400">Not Connected</p>
                    </div>
                  </div>

                  {account.connected ? (
                    <Button
                      variant="outline"
                      size="sm"
                      width="w-full"
                      className="w-full"
                      onClick={() => handlePauseAgent(account.agentId)}
                    >
                      Disconnect
                    </Button>
                  ) : (
                    <Button
                      variant="gradient"
                      size="sm"
                      width="w-full"
                      className="w-full"
                      onClick={() => handleStartAgent(account.platform)}
                    >
                      Connect
                    </Button>
                  )}
                </div>
              ))
            : null}
        </div>
      </div>
    ),
  };

  const tabs = [
    {
      title: "All Accounts",
      value: "all",
      content: tabContent.all,
      step: 1,
    },
    {
      title: "Connected",
      value: "connected",
      content: tabContent.connected,
      step: 2,
    },
    {
      title: "Not Connected",
      value: "disconnected",
      content: tabContent.disconnected,
      step: 3,
    },
  ];
  return (
    <DashboardLayout>
      {activeProject && (
        <div className="bg-violets-are-blue/5 border border-white/5 rounded-3xl p-6">
          <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
            <div>
              <h2 className="text-xl font-semibold text-white mb-2">
                Social Accounts
              </h2>
              <p className="text-gray-300">
                Connect your social media accounts to manage them in one place
              </p>
            </div>
            {user?.email === "<EMAIL>" && (
              <div className="mt-4 md:mt-0">
                <Button
                  variant="gradient"
                  size="sm"
                  onClick={handleStartTwitterBot}
                  disabled={isLoading}
                >
                  Add Twitter Bot
                </Button>
              </div>
            )}
          </div>
          {isLoading && <Loader />}
          <Tabs
            tabs={tabs}
            key={`tabs-${activeProject?.project_id}`}
            containerClassName="mb-6"
            contentClassName="mt-4"
          />
        </div>
      )}
    </DashboardLayout>
  );
}
