'use client'

import { 
  useEffect, useState,
} from 'react';
import { actions } from '@/common/utils/mixpanel';

export const useAnonymousTracking = () => {
  const [anonymousId, setAnonymousId] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Initialize anonymous tracking on first load
    const initializeAnonymousTracking = () => {
      const currentId = actions.getDistinctId();
      setAnonymousId(currentId);
      setIsInitialized(true);

      if (typeof window !== 'undefined') {
        localStorage.setItem('mp_current_distinct_id', currentId);
      }
    };

    initializeAnonymousTracking();
  }, []);

  const trackAnonymousEvent = (eventName: string, properties: Record<string, any> = {}) => {
    if (!isInitialized) {
      return;
    }
    actions.track(eventName, {
      ...properties,
      user_type: 'anonymous',
      anonymous_id: anonymousId,
      timestamp: Date.now(),
    });
  };

  const getTrackingInfo = () => {
    return {
      anonymousId,
      isInitialized,
      currentDistinctId: actions.getDistinctId(),
    };
  };

  return {
    anonymousId,
    isInitialized,
    trackAnonymousEvent,
    getTrackingInfo,
  };
};
