import {
  isProd, mixpanelToken,
} from '@/common/constants';
import mixpanel, { Dict } from 'mixpanel-browser';

if (mixpanelToken) {
  if (isProd) {
    mixpanel.init(mixpanelToken, {
      record_sessions_percent: 1,
    });
    mixpanel.start_session_recording()
  } else {
    mixpanel.init(mixpanelToken);
  }

}
export const actions = {
  identify: (id: string) => {
    mixpanel.identify(id);
  },
  alias: (id: string) => {
    mixpanel.alias(id);
  },
  track: (name: string, props: Dict = {}) => {
    mixpanel.track(name, props);
  },
  people: {
    set: (props: Dict) => {
      mixpanel.people.set(props);
    },
  },
  register: (props: Dict) => {
    mixpanel.register(props);
  },
  // Get the current distinct ID
  getDistinctId: () => {
    return mixpanel.get_distinct_id();
  },
  // Reset the distinct ID (useful for testing)
  reset: () => {
    mixpanel.reset();
  },
};

// Utility function to handle anonymous to authenticated user transition
export const handleUserAuthentication = (userId: string, userProps: Dict = {}) => {
  // Get the current anonymous distinct ID
  const anonymousId = actions.getDistinctId();

  // Store the anonymous ID in localStorage for debugging purposes
  if (typeof window !== 'undefined') {
    localStorage.setItem('mp_anonymous_id', anonymousId);
  }

  // Create an alias linking the anonymous ID to the authenticated user ID
  // This tells Mixpanel that these two IDs represent the same user
  actions.alias(userId);

  // Now identify with the user ID
  actions.identify(userId);

  // Set user properties
  actions.people.set({
    ...userProps,
    $first_login: new Date().toISOString(),
    anonymous_id: anonymousId, // Store the anonymous ID as a property
  });

  // Track the authentication event
  actions.track('User Authenticated', {
    anonymous_id: anonymousId,
    user_id: userId,
    timestamp: Date.now(),
  });
};
