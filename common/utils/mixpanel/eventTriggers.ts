import { Dict } from 'mixpanel-browser';
import { actions } from '.';
import {
  MixpanelEventName, CustomEventProps,
} from './types';

export type { Dict };
export { MixpanelEventName };

export const useMixpanelEvent = () => {
  const mixpanelEvent = ({
    mixpanelProps = {}, eventName,
  }: CustomEventProps) => {
    try {
      const enhancedProps = {
        ...mixpanelProps,
        distinct_id: actions.getDistinctId(),
        session_timestamp: Date.now(),
      };
      actions.track(eventName, enhancedProps);
    } catch (err) {
      console.error('error', `Mixpanel tracking error: ${err}`);
    }
  };

  const trackPostEvent = (eventType: 'scheduled' | 'updated' | 'deleted', data: {
    platforms: string[];
    hasImage?: boolean;
    scheduledTime?: string;
    contentLength?: number;
  }) => {
    const eventMap = {
      scheduled: MixpanelEventName.postScheduled,
      updated: MixpanelEventName.postUpdated,
      deleted: MixpanelEventName.postDeleted,
    };

    mixpanelEvent({
      eventName: eventMap[eventType],
      mixpanelProps: {
        platforms: data.platforms,
        platform_count: data.platforms.length,
        has_image: data.hasImage || false,
        scheduled_time: data.scheduledTime,
        content_length: data.contentLength,
        timestamp: Date.now(),
      },
    });
  };

  const trackPlatformEvent = (eventType: 'connected' | 'disconnected' | 'auth_started', data: {
    platform: string;
    hasOrganizationAccess?: boolean;
  }) => {
    const eventMap = {
      connected: MixpanelEventName.platformConnected,
      disconnected: MixpanelEventName.platformDisconnected,
      auth_started: MixpanelEventName.platformAuthStarted,
    };

    mixpanelEvent({
      eventName: eventMap[eventType],
      mixpanelProps: {
        platform: data.platform,
        has_organization_access: data.hasOrganizationAccess,
        timestamp: Date.now(),
      },
    });
  };

  const trackContentEvent = (eventType: 'content' | 'image' | 'enhance', data: {
    platform?: string;
    prompt?: string;
    contentLength?: number;
    imageStyle?: string;
    enhanceType?: string;
  }) => {
    const eventMap = {
      content: MixpanelEventName.contentGenerated,
      image: MixpanelEventName.imageGenerated,
      enhance: MixpanelEventName.aiEnhanceUsed,
    };

    mixpanelEvent({
      eventName: eventMap[eventType],
      mixpanelProps: {
        platform: data.platform,
        prompt_length: data.prompt?.length,
        content_length: data.contentLength,
        image_style: data.imageStyle,
        enhance_type: data.enhanceType,
        timestamp: Date.now(),
      },
    });
  };

  return {
    mixpanelEvent,
    trackPostEvent,
    trackPlatformEvent,
    trackContentEvent,
  }
}
