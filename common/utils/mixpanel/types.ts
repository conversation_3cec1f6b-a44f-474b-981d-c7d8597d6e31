/* eslint-disable no-shadow */
import { Dict } from 'mixpanel-browser';

export enum MixpanelEventName {
    pageView = 'Page View',
    chatEvent = 'Chat Event',
    tradeTokenEvent = 'Trade Token Event',
    createTokenEvent = 'Create Token Event',

    // Post creation/scheduling events
    postScheduled = 'Post Scheduled',
    postUpdated = 'Post Updated',
    postDeleted = 'Post Deleted',

    // Platform connection events
    platformConnected = 'Platform Connected',
    platformDisconnected = 'Platform Disconnected',
    platformAuthStarted = 'Platform Auth Started',

    // Content/image generation events
    contentGenerated = 'Content Generated',
    imageGenerated = 'Image Generated',
    aiEnhanceUsed = 'AI Enhance Used',
}

export type CustomEventProps = {
    mixpanelProps: Dict,
    eventName: MixpanelEventName,
}
